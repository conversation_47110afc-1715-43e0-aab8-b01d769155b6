<template>
    <div class="chat_list_page">
        <div v-clickoutside="clickoutside">
            <div class="search_box">
                <i class="icon iconfont iconsearch"></i>
                <input
                    type="text"
                    :placeholder="lang.search"
                    @input="inputSearch"
                    v-model="keyword"
                    autocomplete="new-password"
                    @focus="showSearchList = true"
                />
            </div>
            <search-list
                :list.sync="chats"
                v-model="showSearchList"
                :type="0"
                @clearAllData="clearAllData"
                @searchMore="searchMoreChats"
            ></search-list>
        </div>
        <h5 @click="isShowChatList = !isShowChatList" class="container">
            <i v-show="isShowChatList" class="icon iconfont iconsanjiaoxing open"></i>
            <i v-show="!isShowChatList" class="icon iconfont iconsanjiaoxing close"></i>
            {{ lang.recent_chat_text }}（{{ chatList.length }}）
        </h5>
        <vue-scroll class="chat_list" v-loading="!loadingConfig.loadedChatList" v-show="isShowChatList">
            <!-- <div
                @click="openAiChatBox"
                class="chat_item clearfix"
                ref="chat_item_isAiChat"
                :class="{ active: isAiChat }"
            >
                <img src="static/resource_pc/images/ai_logo_small.png" class="avatar fl chat_item_ai_chat" />
                <div class="chat_right fl">
                    <p class="chat_right_title">瑞影·AI+</p>
                </div>
            </div> -->
            <div
                v-if="functionsStatus.live"
                @click="openLiveBox"
                class="chat_item clearfix"
                ref="chat_item_isLiveTraining"
                :class="{ active: isLiveTraining }"
            >
                <img src="static/resource_pc/images/live.png" class="avatar fl" />
                <p class="unread" v-if="liveCount">{{ liveCount }}</p>
                <div class="chat_right fl">
                    <p class="chat_right_title">{{ lang.live }}</p>
                </div>
            </div>
            <div v-if="notify > 0" @click="openApplys" class="chat_item clearfix">
                <img src="static/resource_pc/images/b3-1.png" class="avatar fl" />
                <p class="unread">{{ notify }}</p>
                <div class="chat_right fl">
                    <div class="up">{{ lang.new_apply }}</div>
                    <div class="down">
                        <p class="message">
                            {{ applyTip }}
                        </p>
                    </div>
                </div>
            </div>
            <div
                v-for="chatItem of chatList"
                @click="chatItemHandler(chatItem.cid)"
                :class="{ active: cid == chatItem.cid }"
                :ref="'chat_item_' + chatItem.cid"
                class="chat_item clearfix"
                :key="chatItem.cid"
            >
                <mr-avatar
                    :url="getLocalAvatar(chatItem)"
                    :showOnlineState="checkIsShowOnlineStatus(chatItem)"
                    :onlineState="chatItem.state"
                    :key="chatItem.avatar"
                    :is_public="chatItem.is_public"
                    :service_type="chatItem.service_type"
                ></mr-avatar>
                <template v-if="unreadMap && unreadMap[chatItem.cid]">
                    <span v-if="chatItem.is_mute && unreadMap[chatItem.cid] > 0" class="item_mute"></span>
                    <span v-else-if="unreadMap[chatItem.cid]" class="item_unread">{{
                        unreadMap[chatItem.cid] < 100 ? unreadMap[chatItem.cid] : "99+"
                    }}</span>
                    <!--eslint-disable-line-->
                </template>
                <div class="chat_right">
                    <div class="up">
                        <div class="subject longwrap">
                            <template v-if="!chatItem.service_type">
                                <template v-if="checkIsSingleChat(chatItem)">{{
                                    remarkMap[chatItem.fid] || chatItem.nickname || chatItem.subject
                                }}</template>
                                <template v-else>{{ chatItem.subject }}</template>
                            </template>
                            <template
                                v-else-if="chatItem.service_type == systemConfig.ServiceConfig.type.DrAiAnalyze"
                                >{{ lang.dr_ai_analyze }}</template
                            >
                            <template v-else-if="chatItem.service_type == systemConfig.ServiceConfig.type.AiAnalyze">{{
                                lang.ai_analyze
                            }}</template>
                            <template
                                v-else-if="
                                    chatItem.service_type == systemConfig.ServiceConfig.type.FileTransferAssistant
                                "
                                >{{ lang.file_transfer_assistant }}</template
                            >
                            <template v-if="chatItem.clamped">
                                <el-popover placement="top-start" :title="chatItem.subject" width="200" trigger="hover">
                                    <div slot="reference" class="subject_tip"></div>
                                </el-popover>
                            </template>
                        </div>
                        <span class="send_ts">{{ lastMessage[chatItem.cid].showTime }}</span>
                    </div>
                    <div class="down">
                        <p v-if="chatItem.mentionStatus" class="mention_tip longwrap">{{ lang.someone_mention }}</p>
                        <p v-if="chatItem.is_mute && unreadMap[chatItem.cid] > 0" class="mute_number">
                            [{{ unreadMap[chatItem.cid] }}{{ lang.mute_message_number }}]
                        </p>
                        <p class="attendee_name" v-show="showAttendeeName(chatItem)">
                            {{ remarkMap[lastMessage[chatItem.cid].sender_id] || lastMessage[chatItem.cid].nickname }}：
                        </p>

                        <p
                            class="message"
                            v-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.Text"
                            v-html="lastMessage[chatItem.cid].msg_body"
                        ></p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.EXPIRATION_RES"
                        >
                            {{ lang.ref_res_expired }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.Image"
                        >
                            {{ lang.msg_type_image }} {{ lastMessage[chatItem.cid].file_name }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.Sound"
                        >
                            {{ lang.msg_type_sound }}
                        </p>
                        <p class="message" v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.File">
                            {{ lang.msg_type_file }} {{ lastMessage[chatItem.cid].file_name }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.IWORKS_PROTOCOL"
                        >
                            {{ lang.msg_type_iworks_protocol }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.Video"
                        >
                            {{ lang.msg_type_video }} {{ lastMessage[chatItem.cid].file_name }}
                        </p>
                        <p
                            class="message"
                            v-else-if="
                                lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.OBAI ||
                                lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.Frame ||
                                lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.Cine
                            "
                        >
                            {{ lang.msg_type_consultation_file }} {{ lastMessage[chatItem.cid].img_id }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.RealTimeVideoReview"
                        >
                            {{ lang.realtime_review_msg }} {{ formatTime(lastMessage[chatItem.cid].start_ts) }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.VIDEO_CLIP"
                        >
                            {{ lang.video_clip_msg }} {{ formatTime(lastMessage[chatItem.cid].start_ts) }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.SYS_START_RT_VOICE"
                        >
                            {{ lang.request_voice_chat }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.SYS_STOP_RT_VOICE"
                        >
                            {{ lang.close_voice_chat }}
                        </p>
                        <p
                            class="message"
                            v-else-if="
                                lastMessage[chatItem.cid].msg_type ==
                                systemConfig.msg_type.SYS_START_REALTIME_CONSULTATION
                            "
                        >
                            {{ lang.request_realtime_video }}
                        </p>
                        <p
                            class="message"
                            v-else-if="
                                lastMessage[chatItem.cid].msg_type ==
                                systemConfig.msg_type.SYS_STOP_REALTIME_CONSULTATION
                            "
                        >
                            {{ lang.close_realtime_video }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.SYS_JOIN_ATTENDEE"
                        >
                            {{
                                lastMessage[chatItem.cid].attendee_changed_info &&
                                lastMessage[chatItem.cid].attendee_changed_info.nickname
                            }}
                            {{ lang.join_group_tip }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.SYS_KICKOUT_ATTENDEE"
                        >
                            {{
                                lastMessage[chatItem.cid].attendee_changed_info &&
                                lastMessage[chatItem.cid].attendee_changed_info.nickname
                            }}
                            {{ lang.exit_group_tip }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.COMMENT"
                        >
                            {{ lang.add_comment_msg_text }}：{{ lastMessage[chatItem.cid].comment }}
                        </p>
                        <p
                            class="message"
                            v-else-if="
                                lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.TAG &&
                                lastMessage[chatItem.cid].action == 1
                            "
                        >
                            {{ lang.add_tag_msg_text }}：{{ lastMessage[chatItem.cid].tags }}
                        </p>
                        <p
                            class="message"
                            v-else-if="
                                lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.TAG &&
                                lastMessage[chatItem.cid].action == 2
                            "
                        >
                            {{ lang.delete_tag_msg_text }}：{{ lastMessage[chatItem.cid].tags }}
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.AI_ANALYZE"
                        >
                            {{ lang.analyze_result_tip }}...
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.IWORKS_SCORE"
                        >
                            {{ lang.iworks_score_label }}...
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.SYS_CONFERENCE_PLAN"
                        >
                            <template
                                v-if="lastMessage[chatItem.cid].tip_type == systemConfig.conference_plan_tip_type.New"
                            >
                                {{ lang.reserved_conference_tip
                                }}{{
                                    lastMessage[chatItem.cid].conference_plan &&
                                    lastMessage[chatItem.cid].conference_plan.subject
                                }}
                            </template>
                            <template
                                v-if="
                                    lastMessage[chatItem.cid].tip_type == systemConfig.conference_plan_tip_type.Prepare
                                "
                            >
                                {{ lang.conference_begin_tip
                                }}{{
                                    lastMessage[chatItem.cid].conference_plan &&
                                    lastMessage[chatItem.cid].conference_plan.subject
                                }}
                            </template>
                            <template
                                v-if="
                                    lastMessage[chatItem.cid].tip_type == systemConfig.conference_plan_tip_type.Cancel
                                "
                            >
                                {{ lang.delete_conference_tip
                                }}{{
                                    lastMessage[chatItem.cid].conference_plan &&
                                    lastMessage[chatItem.cid].conference_plan.subject
                                }}
                            </template>
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.WITHDRAW"
                        >
                            <template v-if="lastMessage[chatItem.cid].sender_id === user.uid">{{
                                lang.revocation_message_by_self
                            }}</template>
                            <template v-else
                                ><span>{{ lastMessage[chatItem.cid].nickname }}</span
                                >{{ lang.revocation_message_by_other }}</template
                            >
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.ResourceDelete"
                        >
                            <template v-if="lastMessage[chatItem.cid].sender_id === user.uid">{{
                                lang.delete_message_by_self
                            }}</template>
                            <template v-else
                                ><span>{{ lastMessage[chatItem.cid].nickname }}</span
                                >{{ lang.delete_message_by_other }}</template
                            >
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.LIVE_INVITE"
                        >
                            <template>{{ getLiveInviteStatusStr(lastMessage[chatItem.cid]) }}</template>
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.EXAM_IMAGES"
                        >
                            <template>{{ lang.exam_images_title }}</template>
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.MULTICENTER_REJECT"
                        >
                            <template>[{{ lang.multicenter_reject_tip }}]</template>
                        </p>
                        <p
                            class="message"
                            v-else-if="lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.HOMEWORK_DETAIL || lastMessage[chatItem.cid].msg_type == systemConfig.msg_type.HOMEWORK_DETAIL_INDIVIDUAL"
                        >
                            <template>[{{ lang.cloud_exam }}]</template>
                        </p>
                        <p class="message" v-else-if="lastMessage[chatItem.cid].msg_type">
                            {{ lang.unsupport_msg_type }}：{{ lastMessage[chatItem.cid].msg_type }}
                        </p>
                        <p v-else></p>
                        <div class="icon_bar fr">
                            <i
                                class="iconfont iconvideo fr"
                                v-if="
                                    functionsStatus.live &&
                                    liveConference[chatItem.cid] &&
                                    liveConference[chatItem.cid].conferenceState
                                "
                            ></i>
                        </div>
                    </div>
                </div>
            </div>
        </vue-scroll>
        <div class="recent_bar clearfix">
            <div :class="{ active: recentIndex == 1 }" @click="recentIndex = 1">
                {{ lang.recent_images }}
            </div>
        </div>
        <div class="image_list" v-show="recentIndex == 1">
            <gallery-file-list
                :galleryList="consultationImageList"
                :span="3"
                v-loading="!loadingConfig.loadedFileList"
                :finished="consultationImageListIndex <= 0"
                @loadMore="loadMore"
                :isMini="true"
                :from="'consultationImageList'"
                ref="consultationImageList"
                :showMonth="true"
            >
            </gallery-file-list>
        </div>
    </div>
</template>
<script>
import base from "../lib/base";
import iworksTool from '../lib/iworksTool';
import searchList from "./searchList";
import Clickoutside from "element-ui/src/utils/clickoutside";
import { parseImageListToLocal, patientDesensitization, getLocalAvatar ,setIworksInfoToMsg,setClamped} from "../lib/common_base";
import GalleryFileList from "./galleryFileList.vue";
export default {
    mixins: [base,iworksTool],
    name: "LeftTabChat",
    components: {
        searchList,
        GalleryFileList,
    },
    directives: { Clickoutside },
    data() {
        return {
            getLocalAvatar,
            isShowChatList: true,
            recentIndex: 1,
            loadingConfig: this.$store.state.loadingConfig,
            applyTip: "",
            showTotalList: [],
            chats: [],
            filterTimer: null,
            beforeKeyword: "",
            keyword: "",
            showSearchList: false,
        };
    },
    mounted() {
        this.$nextTick(() => {
            this.$root.eventBus.$off("changeSelectChat").$on("changeSelectChat", ({ cid }) => {
                this.$root.eventBus.$emit("changeTab", 1);
                setTimeout(() => {
                    // 获取聊天列表容器并滚动到顶部
                    const chatList = this.$el.querySelector('.chat_list');
                    if (chatList) {
                        chatList.scrollTop = 0;
                    }
                }, 0);
            });
        });
    },
    computed: {
        cid() {
            return this.$route.params.cid;
        },
        chatList() {
            let chatList = [];
            this.$store.state.chatList.list.forEach((item) => {
                setClamped(item);
                if (item.service_type !== this.systemConfig.ServiceConfig.type.LiveBroadCast) {
                    //
                    chatList.push(item);
                }
            });
            return chatList;
        },
        lastMessage() {
            return this.$store.state.chatList.lastMessage;
        },
        subjectObj() {
            let obj = {};
            for (let item of this.chatList) {
                obj[item.cid] = item.subject;
            }
            return obj;
        },
        consultationImageList() {
            return this.$store.state.consultationImageList.list;
        },
        consultationImageListIndex() {
            return this.$store.state.consultationImageList.index;
        },
        consultationImageListCount() {
            return this.$store.state.consultationImageList.total_count;
        },
        notifications() {
            return this.$store.state.notifications;
        },
        friendApply() {
            return this.notifications.friendApply;
        },
        groupApply() {
            return this.notifications.groupApply;
        },
        notify() {
            let sum = 0;
            if (this.friendApply.length) {
                sum = sum + this.friendApply.length;
                this.applyTip =
                    this.friendApply[this.friendApply.length - 1].param.nickname + this.lang.apply_friend_txt;
            }
            if (this.groupApply.length) {
                sum = sum + this.groupApply.length;
                this.applyTip =
                    this.groupApply[this.groupApply.length - 1].nickname +
                    this.lang.apply_group_txt +
                    this.groupApply[this.groupApply.length - 1].subject;
            }
            return sum;
        },
        rowChatList() {
            return this.$store.state.chatList;
        },
        liveCount() {
            if (Object.keys(this.$store.state.notifications.liveCount).length > 0) {
                return (
                    this.$store.state.notifications.liveCount.startCount +
                    this.$store.state.notifications.liveCount.waittingCount
                );
            } else {
                return 0;
            }
        },
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        isLiveTraining() {
            return this.cid == "live_training";
        },
        isAiChat() {
            return this.cid == "ai_chat";
        },
        liveConference() {
            return this.$store.state.liveConference;
        },
        unreadMap() {
            return this.$store.state.chatList.unreadMap;
        },
    },
    methods: {
        loadMore(callback) {
            let controller = window.main_screen.controller;
            controller.emit(
                "get_consultation_image_list",
                {
                    start: this.consultationImageListIndex,
                    count: this.systemConfig.consultationImageShowNum,
                },
                (is_succ, data) => {
                    console.log("get_consultation_image_list",data)
                    this.$refs.consultationImageList.loadFinished();
                    if (is_succ) {
                        patientDesensitization(data.consultation_image_list);
                        parseImageListToLocal(data.consultation_image_list, "url");
                        if(data.iworks_protocol_list){
                            // this.setGalleryMessageDetail(is_succ,{iworks_protocol_list:data.iworks_protocol_list})
                            //将评论信息、iworks协议放入画廊中
                            for(let key in data.iworks_protocol_list){
                                let protocol=data.iworks_protocol_list[key];
                                protocol.protocolTree=[this.setProtocolTree(protocol)]
                                protocol.viewList=this.setViewList(protocol.protocolTree[0],[])
                            }
                            this.$store.commit('gallery/setCommentToGallery',data)
                        }
                        let consultation_image_list = data.consultation_image_list
                        for (let item of  data.consultation_image_list) {
                            setIworksInfoToMsg(item);
                        }
                        this.$store.commit("consultationImageList/pushMoreConsultationImages", data);
                        if (this.consultationImageList.length == this.consultationImageListCount) {
                            this.$message.success(this.lang.image_all_loaded);
                        }
                        callback && callback(data);
                    }
                }
            );
        },

        chatItemHandler(cid) {
            this.toggleChatItem(cid);
        },
        toggleChatItem(cid) {
            this.openConversation(cid, 1, {}, (is_success, data) => {
                if (!is_success) {
                    return;
                }
                if (
                    this.functionsStatus.live &&
                    this.$store.state.liveConference[cid] &&
                    this.$store.state.liveConference[cid].conferenceState
                ) {
                    //该群有直播，进群自动接起
                    setTimeout(() => {
                        if (Number(cid) !== Number(this.cid)) {
                            return;
                        }
                        this.$root.eventBus.$emit("acceptLiveConference");
                    }, 600);
                }
            });
        },
        openApplys() {
            this.$router.push(this.$route.fullPath + "/applys");
        },
        getShowDes(name) {
            if (name.length > 8) {
                name = name.substring(0, 8) + "......";
            }
            return name;
        },
        inputSearch(e) {
            let val = e.target.value;
            this.filterTimer && clearTimeout(this.filterTimer);
            // 防抖处理
            this.filterTimer = setTimeout(() => {
                const keyword = val.trim();
                if (keyword) {
                    if (keyword !== this.beforeKeyword) {
                        this.beforeKeyword = keyword;
                        this.searchChatList(keyword, this.rowChatList);
                    } else {
                        // 值与之前一样，不做任何操作
                    }
                } else {
                    this.beforeKeyword = "";
                    this.clearAllData();
                }
            }, 300);
        },
        searchChatList(keyword, chatList) {
            const re = new RegExp(`${keyword}`, "ig");
            this.chats = [];
            let filterObj = {};
            const keywordLength = keyword.length;
            for (const item of chatList.list) {
                // 如果是群落，深度遍历
                if (item.type === 3 && item.list.length > 0) {
                    for (const chat of item.list) {
                        if (chat.cid in filterObj) {
                            // 去重
                            continue;
                        }
                        filterObj[chat.cid] = "";
                        if (chat.type === 3 && item.list.length > 0) {
                            // 如果还是群落，则继续深度遍历
                            this.searchChatList(keyword, chat.list);
                        } else {
                            let itemCopy = { ...chat };
                            let subject = this.remarkMap[itemCopy.fid] || itemCopy.subject;
                            if (re.test(subject)) {
                                const index = subject.search(re); // 找到匹配到的字符的第一个索引
                                const subStr = subject.substring(index, index + keywordLength); // 从原昵称切割
                                re.lastIndex = 0;

                                itemCopy.subject = subject.replace(subStr, `<span class="lighten">${subStr}</span>`);
                                // 最多只展示4条数据
                                if (this.chats.length < 4) {
                                    this.chats.push(itemCopy);
                                } else {
                                    return;
                                }
                            }
                        }
                    }
                } else {
                    // 是好友或群聊，深拷贝一份再操作，避免破坏源对象结构
                    let itemCopy = { ...item };
                    let subject = this.remarkMap[itemCopy.fid] || itemCopy.subject;
                    if (re.test(subject)) {
                        const index = subject.search(re); // 找到匹配到的字符的第一个索引
                        const subStr = subject.substring(index, index + keywordLength); // 从原昵称切割
                        re.lastIndex = 0;

                        itemCopy.subject = subject.replace(subStr, `<span class="lighten">${subStr}</span>`);
                        // 最多只展示4条数据
                        if (this.chats.length < 4) {
                            this.chats.push(itemCopy);
                        } else {
                            return;
                        }
                    }
                }
            }
        },
        clearAllData() {
            this.keyword = "";
            this.beforeKeyword = "";
            this.chats = [];
        },
        searchMoreChats() {
            this.$root.eventBus.$emit("searchMore", {
                searchText: this.keyword,
                activeName: "5",
            });
        },
        openLiveBox() {
            this.$router.push("/main/education/live_training");
        },
        getCommentNum(commentList = []) {
            let num = 0;
            commentList.forEach((element) => {
                if (element.status !== 0) {
                    if (!element.is_private || (element.is_private && this.user.uid === element.author_id)) {
                        num++;
                    }
                }
            });
            return num;
        },
        getLiveInviteStatusStr(msg) {
            return this.lang[`live_invite_status${msg.liveInfo.status}`].replace("{1}", msg.liveInfo.creator_name);
        },
        checkIsSingleChat(item) {
            const Single = this.systemConfig.ConversationConfig.type.Single;
            return item.type === Single;
        },
        checkIsShowOnlineStatus(item) {
            if (this.checkIsSingleChat(item) && item.service_type === 0) {
                return true;
            }
            return false;
        },
        showAttendeeName(chatItem) {
            return (
                !this.checkIsSingleChat(chatItem) &&
                this.lastMessage[chatItem.cid].msg_type != undefined &&
                this.lastMessage[chatItem.cid].msg_type != this.systemConfig.msg_type.SYS_JOIN_ATTENDEE &&
                this.lastMessage[chatItem.cid].msg_type != this.systemConfig.msg_type.SYS_KICKOUT_ATTENDEE &&
                this.lastMessage[chatItem.cid].msg_type != this.systemConfig.msg_type.WITHDRAW &&
                this.lastMessage[chatItem.cid].msg_type != this.systemConfig.msg_type.ResourceDelete
            );
        },
        clickoutside() {
            this.$nextTick(() => {
                this.showSearchList = false;
            });
        },
        openAiChatBox() {
            this.$router.push("/main/index/chat_window/ai_chat");
        },
    },
};
</script>
<style lang="scss">
.chat_list_page {
    min-height: 0;
    will-change: opacity; //safari 兼容性代码
    opacity: 1; //safari兼容性代码
    .search_box {
        width: 100%;
        height: 32px;
        padding: 0 10px;
        line-height: 32px;
        border-bottom: 1px solid #dbdbdb;
        i,
        input {
            vertical-align: middle;
            vertical-align: middle;
            height: 100%;
        }
        input {
            margin-left: 5px;
            color: #666;
            width: 90%;
        }
    }
    .chat_item {
        .item_unread {
            color: #fff;
            background: #f00;
            min-width: 18px;
            display: inline-block;
            height: 18px;
            font-size: 12px;
            text-align: center;
            line-height: 18px;
            border-radius: 50%;
            position: absolute;
            left: 52px;
            top: 10px;
        }
        .item_mute {
            background: #f00;
            width: 8px;
            display: inline-block;
            height: 8px;
            border-radius: 50%;
            position: absolute;
            left: 56px;
            top: 14px;
        }
        .mention_tip {
            font-size: 14px;
            color: #f00;
            line-height: 30px;
            margin-right: 4px;
            text-overflow: initial;
            overflow: visible;
        }
        .mute_number {
            font-size: 14px;
            color: #555;
            line-height: 30px;
            margin-right: 4px;
            flex-shrink: 0;
        }
    }
    .recent_bar {
        display: flex;
        font-size: 15px;
        & > div {
            width: 50%;
            flex: 1;
            text-align: center;
            border: 1px solid #bfd1d1;
            cursor: pointer;
            &:hover {
                background-color: #bfd1d1;
            }
            &.active {
                background-color: #a1b7b6;
            }
        }
        .total_unread {
            background-color: #f00;
            color: #fff;
            border-radius: 50%;
            font-size: 12px;
            min-width: 14px;
            min-height: 14px;
            display: inline-block;
            line-height: 12px;
            text-align: center;
            padding: 1px 2px;
        }
    }
    .image_list {
        flex: 2;
        padding-left: 5px;
        overflow-y: auto;
    }
    .consultation_list {
        flex: 2;
        overflow: auto;
        .recent_consult_item {
            height: 68px;
            position: relative;
            padding-left: 8px;
            cursor: pointer;
            border-bottom: 1px solid #dbdbdb;
            .avatar {
                width: 46px;
                height: 46px;
                margin: 10px 10px 10px 20px;
                border-radius: 50%;
            }
            .chat_right {
                width: 215px;
                padding: 0 8px;
                height: 46px;
                margin: 10px 0;
                .up {
                    display: flex;
                    .subject {
                        flex: 1;
                        height: 22px;
                        overflow: hidden;
                    }
                    .send_ts {
                        font-size: 12px;
                        line-height: 22px;
                        margin-left: 6px;
                        width: 64px;
                        text-align: right;
                    }
                }
                .down {
                    font-size: 14px;
                    color: #666;
                    margin-top: 4px;
                    overflow: hidden;
                }
            }
        }
    }
}
</style>
