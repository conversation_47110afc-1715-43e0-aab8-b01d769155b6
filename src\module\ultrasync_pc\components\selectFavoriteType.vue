<template>
    <div>
        <!-- <el-dialog
            class="select_favorite_dialog"
            :title="lang.select_placeholder_text"
            :visible="visible"
            :close-on-click-modal="false"
            width="450px"
            :append-to-body="true"
            :modal="false"
            :before-close="closeSelectFavoriteDialog"
            @close="dialogClose"
            >
            <div class="container clearfix">
                <el-radio-group v-model="selectFavoriteType" @input="handleSelectFavoriteTypeChange">
                    <el-radio @change="toPersonalFavorite" :label="1" border>{{lang.personal_favorite_text}}</el-radio>
                    <el-radio @click="toGroupFavorite" :label="2" border v-if="showGroupFavorite">{{lang.group_favorite_text}}</el-radio>
                </el-radio-group>
                <div v-if="selectFavoriteType===1" class="group_favorite_wrap">
                    <p class="favorite_tip">{{lang.favorite_confirm_tip}}</p>
                    <el-radio-group v-model="publicStatus" @input="changeFavoriteType">
                        <el-radio :label="1">{{lang.favorite_confirm_public}}</el-radio>
                        <el-radio :label="0">{{lang.favorite_confirm_private}}</el-radio>
                    </el-radio-group>
                </div>
                <div v-if="selectFavoriteType===2" class="group_favorite_wrap">
                    <group-favorite :edit="true" ref="menu_group_favorite" :cid="message.group_id" @finishedEdit="finishedEdit" :key="message.group_id"></group-favorite>
                </div>
            </div>
            <template #footer>  -->
        <!-- group-favorite 输入完之后才可以进行提交 否则就要提醒 -->
        <!-- <el-button v-if="selectFavoriteType===1" @click="closeSelectFavoriteDialog" :disabled="saveFavoriteLoaingForPersonal">{{lang.cancel_btn}}</el-button>
                <el-button v-if="selectFavoriteType===1" type="primary" @click="saveFavoriteCommit" v-loading="saveFavoriteLoading" :disabled="saveFavoriteLoaingForPersonal">{{lang.confirm_txt}}</el-button>
                <el-button v-if="selectFavoriteType===2" @click="closeSelectFavoriteDialog" :disabled="saveFavoriteLoaingForGroup">{{lang.cancel_btn}}</el-button>
                <el-button v-if="selectFavoriteType===2" type="primary" @click="saveFavoriteCommit" v-loading="saveFavoriteLoading" :disabled="saveFavoriteLoaingForGroup">{{lang.confirm_txt}}</el-button>
            </template>
            </el-dialog> -->

        <CommonDialog
            class="select_favorite_dialog"
            :title="lang.select_placeholder_text"
            :show.sync="visible"
            :close-on-click-modal="false"
            width="450px"
            :append-to-body="true"
            :modal="false"
            @closed="closeSelectFavoriteDialog"
            @close="dialogClose"
        >
            <div class="container clearfix">
                <el-radio-group v-model="selectFavoriteType" @input="handleSelectFavoriteTypeChange">
                    <el-radio @change="toPersonalFavorite" :label="1" border>{{
                        lang.personal_favorite_text
                    }}</el-radio>
                    <el-radio @click="toGroupFavorite" :label="2" border v-if="showGroupFavorite">{{
                        lang.group_favorite_text
                    }}</el-radio>
                </el-radio-group>
                <div v-if="selectFavoriteType === 1" class="group_favorite_wrap">
                    <p class="favorite_tip">{{ lang.favorite_confirm_tip }}</p>
                    <el-radio-group v-model="publicStatus" @input="changeFavoriteType">
                        <el-radio :label="1">{{ lang.favorite_confirm_public }}</el-radio>
                        <el-radio :label="0">{{ lang.favorite_confirm_private }}</el-radio>
                    </el-radio-group>
                </div>
                <div v-if="selectFavoriteType === 2" class="group_favorite_wrap">
                    <group-favorite
                        :edit="true"
                        ref="menu_group_favorite"
                        :cid="message.group_id"
                        @finishedEdit="finishedEdit"
                        :key="message.group_id"
                    ></group-favorite>
                </div>
            </div>
            <template #footer>
                <!-- group-favorite 输入完之后才可以进行提交 否则就要提醒 -->
                <div class="smallBtnContainer">
                    <el-button
                        v-if="selectFavoriteType === 1"
                        @click="closeSelectFavoriteDialog"
                        :disabled="saveFavoriteLoaingForPersonal"
                        >{{ lang.cancel_btn }}</el-button
                    >
                    <el-button
                        v-if="selectFavoriteType === 1"
                        type="primary"
                        @click="saveFavoriteCommit"
                        v-loading="saveFavoriteLoading"
                        :disabled="saveFavoriteLoaingForPersonal"
                        >{{ lang.confirm_txt }}</el-button
                    >
                    <el-button
                        v-if="selectFavoriteType === 2"
                        @click="closeSelectFavoriteDialog"
                        :disabled="saveFavoriteLoaingForGroup"
                        >{{ lang.cancel_btn }}</el-button
                    >
                    <el-button
                        v-if="selectFavoriteType === 2"
                        type="primary"
                        @click="saveFavoriteCommit"
                        v-loading="saveFavoriteLoading"
                        :disabled="saveFavoriteLoaingForGroup"
                        >{{ lang.confirm_txt }}</el-button
                    >
                </div>
            </template>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import service from "../service/service";
import GroupFavorite from "./groupFavorite.vue";
import CommonDialog from "../MRComponents/commonDialog.vue";

export default {
    mixins: [base],
    name: "selectFavoriteType",
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        message: {
            type: Object,
            default: () => {
                return {};
            },
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        GroupFavorite,
        CommonDialog,
    },
    watch: {
        value: {
            handler(val) {
                if (val) {
                    this.showSelectFavoriteDialog();
                } else {
                    this.visible = val;
                }
            },
            immediate: true,
        },
        visible: {
            handler(val) {
                this.$emit("change", val);
            },
        },
    },
    computed: {
        cid() {
            return this.$route.query.cid || this.$route.params.cid;
        },
        showGroupFavorite() {
            return (
                this.message && this.message.from !== "live_training" && this.message.from !== "consultationImageList"
            );
        },
    },
    created() {},
    data() {
        return {
            visible: false,
            selectFavoriteType: 1,
            saveFavoriteLoaingForPersonal: false,
            saveFavoriteLoaingForGroup: false,
            saveFavoriteLoading: false,
            publicStatus: 0,
        };
    },
    methods: {
        saveFavoriteAction(msg) {
            var operatTimer = setTimeout(() => {
                this.$message.success(this.lang.operating_text);
            }, 500);
            service
                .addFavorite({
                    resourceIDList: [msg.resource_id],
                    publicStatus: this.publicStatus,
                })
                .then((res) => {
                    clearTimeout(operatTimer);
                    if (res.data.error_code == 0) {
                        this.closeSelectFavoriteDialog();
                        let userFavoriteIdList = res.data.data;
                        this.$message.success(this.lang.add_favorites_success);
                        //同步各图像列表收藏状态
                        this.$store.commit("resourceTempStatus/updateResourceTempStatus", {
                            resource_id: msg.resource_id,
                            data: {
                                userFavoriteStatus: true,
                            },
                        });
                        service
                            .updatePublicStatus({
                                userFavoriteIdList: userFavoriteIdList,
                                publicStatus: this.publicStatus,
                            })
                            .then((res) => {});
                    } else {
                        this.$message.error(this.lang.add_favorites_fail);
                    }
                });
        },
        async saveFavoriteCommit() {
            if (this.selectFavoriteType === 1) {
                this.saveFavoriteAction(this.message);
            } else {
                try {
                    const category_data = await this.$refs["menu_group_favorite"].insertResourceToCategory(
                        this.message
                    );
                    if (this.selectFavoriteType === 1) {
                        this.saveFavoriteLoaingForPersonal = false;
                    } else {
                        this.saveFavoriteLoaingForGroup = false;
                    }
                    this.closeSelectFavoriteDialog();
                    if (String(this.message.group_id) === this.$route.params.cid) {
                        this.$root.eventBus.$emit("insertCollection", {
                            file: this.message,
                            category_data,
                        });
                    }
                } catch (error) {
                    console.error(error);
                }
            }
        },
        async showSelectFavoriteDialog() {
            try {
                const lastSelectType = await window.mainDB.userOptions.get("selectFavoriteType");
                console.log(lastSelectType.value);
                if (this.showGroupFavorite) {
                    this.selectFavoriteType = lastSelectType.value || 1;
                } else {
                    this.selectFavoriteType = 1;
                }
            } catch (error) {
                this.selectFavoriteType = 1;
            } finally {
                this.visible = true;
            }
        },
        closeSelectFavoriteDialog() {
            this.$root.eventBus.$emit("showRealTimeVideo");
            this.visible = false;
        },
        changeFavoriteType(data) {
            console.log(data);
        },
        handleSelectFavoriteTypeChange(data) {
            window.mainDB.userOptions.put({
                id: "selectFavoriteType",
                value: data,
            });
        },
        finishedEdit(isFinishedEdit) {
            this.saveFavoriteLoaingForGroup = !isFinishedEdit;
        },
        dialogClose() {
            if (this.selectFavoriteType === 1) {
                this.saveFavoriteLoaingForPersonal = false;
            } else {
                this.saveFavoriteLoaingForGroup = false;
            }
        },
        toPersonalFavorite() {
            this.saveFavoriteLoaingForGroup = false;
        },
        toGroupFavorite() {
            this.saveFavoriteLoaingForPersonal = false;
        },
    },
};
</script>
<style lang="scss">
.select_favorite_dialog {
    .el-dialog {
        height: auto !important;
    }
    .container {
        .item {
            display: flex;
            margin: 10px 0;
            .title {
                line-height: 40px;
                min-width: 120px;
            }
            .form_item {
                flex: 1;
            }
            .choose_directory {
                margin-left: 10px;
            }
        }
    }
    .process_modal {
        position: absolute;
        z-index: 2;
        background: rgba(256, 256, 256, 0.7);
        width: 100%;
        top: -36px;
        left: 0;
        bottom: 0;
        padding: 20px;
        .el-progress {
            top: 50%;
        }
    }
}
.group_favorite_wrap {
    margin-top: 20px;
    height: 400px;
    overflow: hidden;
    .favorite_tip {
        font-size: 14px;
        margin-bottom: 10px;
    }
    .el-radio {
        margin-bottom: 6px;
    }
}
</style>
